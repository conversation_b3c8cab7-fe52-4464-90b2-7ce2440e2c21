<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 用户管理控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\model\User;
use think\Response;

/**
 * 用户管理控制器
 */
class UserController extends BaseController
{
    protected function initialize(): void
    {
        parent::initialize();
    }

    /**
     * 用户列表
     * @return Response
     */
    public function index(): Response
    {
        try {
            $page = $this->request->param('page', 1);
            $limit = $this->request->param('limit', 15);
            $keyword = $this->request->param('keyword', '');

            $query = User::with(['roles']);

            if (!empty($keyword)) {
                $query->where(function($q) use ($keyword) {
                    $q->whereLike('username', "%{$keyword}%")
                      ->whereOr('email', 'like', "%{$keyword}%")
                      ->whereOr('real_name', 'like', "%{$keyword}%");
                });
            }

            $result = $query->order('id', 'desc')
                          ->paginate([
                              'list_rows' => $limit,
                              'page' => $page
                          ]);

            return $this->success($result->toArray(), '获取用户列表成功');

        } catch (\Exception $e) {
            return $this->error('获取用户列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 用户详情
     * @param int $id 用户ID
     * @return Response
     */
    public function read(int $id): Response
    {
        try {
            $user = User::with(['roles'])->find($id);
            if (!$user) {
                return $this->error('用户不存在', 404);
            }

            return $this->success($user->toArray(), '获取用户详情成功');

        } catch (\Exception $e) {
            return $this->error('获取用户详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建用户
     * @return Response
     */
    public function save(): Response
    {
        try {
            $data = $this->request->post();

            // 基本验证
            if (empty($data['username'])) {
                return $this->error('用户名不能为空');
            }
            if (empty($data['email'])) {
                return $this->error('邮箱不能为空');
            }
            if (empty($data['password'])) {
                return $this->error('密码不能为空');
            }

            // 检查用户名是否存在
            if (User::where('username', $data['username'])->count() > 0) {
                return $this->error('用户名已存在');
            }

            // 检查邮箱是否存在
            if (User::where('email', $data['email'])->count() > 0) {
                return $this->error('邮箱已存在');
            }

            // 密码加密
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
            $data['status'] = $data['status'] ?? 1;

            // 创建用户
            $user = User::create($data);

            return $this->success($user->toArray(), '用户创建成功');

        } catch (\Exception $e) {
            return $this->error('用户创建失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新用户
     * @param int $id 用户ID
     * @return Response
     */
    public function update(int $id): Response
    {
        try {
            $user = User::find($id);
            if (!$user) {
                return $this->error('用户不存在', 404);
            }

            $data = $this->request->post();

            // 如果更新用户名，检查是否重复
            if (isset($data['username']) && $data['username'] !== $user->username) {
                if (User::where('username', $data['username'])->count() > 0) {
                    return $this->error('用户名已存在');
                }
            }

            // 如果更新邮箱，检查是否重复
            if (isset($data['email']) && $data['email'] !== $user->email) {
                if (User::where('email', $data['email'])->count() > 0) {
                    return $this->error('邮箱已存在');
                }
            }

            // 如果更新密码，进行加密
            if (isset($data['password']) && !empty($data['password'])) {
                $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
            } else {
                unset($data['password']);
            }

            // 更新用户
            $user->save($data);

            return $this->success($user->toArray(), '用户更新成功');

        } catch (\Exception $e) {
            return $this->error('用户更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除用户
     * @param int $id 用户ID
     * @return Response
     */
    public function delete(int $id): Response
    {
        try {
            $user = User::find($id);
            if (!$user) {
                return $this->error('用户不存在', 404);
            }

            // 不能删除超级管理员
            if ($user->hasRole('super_admin')) {
                return $this->error('不能删除超级管理员');
            }

            // 软删除用户
            $user->delete();

            return $this->success([], '用户删除成功');

        } catch (\Exception $e) {
            return $this->error('用户删除失败: ' . $e->getMessage());
        }
    }



    /**
     * 获取用户统计
     * @return Response
     */
    public function statistics(): Response
    {
        try {
            $stats = [
                'total' => User::count(),
                'enabled' => User::where('status', 1)->count(),
                'disabled' => User::where('status', 0)->count(),
                'today_new' => User::whereTime('created_at', 'today')->count(),
                'this_week_new' => User::whereTime('created_at', 'week')->count(),
                'this_month_new' => User::whereTime('created_at', 'month')->count(),
            ];

            return $this->success($stats, '获取用户统计成功');

        } catch (\Exception $e) {
            return $this->error('获取用户统计失败: ' . $e->getMessage());
        }
    }
}
