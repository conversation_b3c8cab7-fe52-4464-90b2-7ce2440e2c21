<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 操作日志模型
 */

declare(strict_types=1);

namespace app\model;

use think\Model;

/**
 * 操作日志模型
 * @property int $id 日志ID
 * @property int $user_id 用户ID
 * @property string $action 操作类型
 * @property string $resource 资源类型
 * @property int $resource_id 资源ID
 * @property string $data 操作数据
 * @property string $ip_address IP地址
 * @property string $user_agent 用户代理
 * @property string $created_at 创建时间
 */
class Log extends Model
{
    // 表名
    protected $name = 'logs';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'created_at';
    protected $updateTime = false;

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'user_id' => 'integer',
        'resource_id' => 'integer',
        'created_at' => 'datetime'
    ];

    // 允许批量赋值的字段
    protected $field = [
        'user_id',
        'action',
        'resource',
        'resource_id',
        'data',
        'ip_address',
        'user_agent'
    ];

    /**
     * 关联用户
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 数据获取器
     * @param string $value
     * @return array
     */
    public function getDataAttr(string $value): array
    {
        return json_decode($value, true) ?? [];
    }

    /**
     * 数据设置器
     * @param mixed $value
     * @return string
     */
    public function setDataAttr($value): string
    {
        if (is_array($value)) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        return $value;
    }

    /**
     * 搜索范围
     * @param \think\db\Query $query
     * @param string $keyword
     * @return void
     */
    public function scopeSearch($query, string $keyword): void
    {
        if (!empty($keyword)) {
            $query->where(function ($q) use ($keyword) {
                $q->whereLike('action', "%{$keyword}%")
                  ->whereOr('resource', 'like', "%{$keyword}%")
                  ->whereOr('ip_address', 'like', "%{$keyword}%");
            });
        }
    }

    /**
     * 用户范围
     * @param \think\db\Query $query
     * @param int $userId
     * @return void
     */
    public function scopeUser($query, int $userId): void
    {
        $query->where('user_id', $userId);
    }

    /**
     * 操作类型范围
     * @param \think\db\Query $query
     * @param string $action
     * @return void
     */
    public function scopeAction($query, string $action): void
    {
        $query->where('action', $action);
    }

    /**
     * 资源类型范围
     * @param \think\db\Query $query
     * @param string $resource
     * @return void
     */
    public function scopeResource($query, string $resource): void
    {
        $query->where('resource', $resource);
    }

    /**
     * 时间范围
     * @param \think\db\Query $query
     * @param string $startTime
     * @param string $endTime
     * @return void
     */
    public function scopeTimeRange($query, string $startTime, string $endTime): void
    {
        if (!empty($startTime)) {
            $query->where('created_at', '>=', $startTime);
        }
        if (!empty($endTime)) {
            $query->where('created_at', '<=', $endTime);
        }
    }

    /**
     * 记录操作日志
     * @param array $data 日志数据
     * @return static
     */
    public static function record(array $data): self
    {
        return self::create($data);
    }

    /**
     * 清理过期日志
     * @param int $days 保留天数
     * @return int
     */
    public static function cleanup(int $days = 30): int
    {
        $expireTime = date('Y-m-d H:i:s', time() - $days * 86400);
        return self::where('created_at', '<', $expireTime)->delete();
    }
}
