<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 用户模型
 */

declare(strict_types=1);

namespace app\model;

use think\Model;
use think\model\concern\SoftDelete;
use think\model\relation\BelongsToMany;

/**
 * 用户模型
 * @property int $id 用户ID
 * @property string $username 用户名
 * @property string $email 邮箱
 * @property string $password 密码
 * @property string $avatar 头像
 * @property string $real_name 真实姓名
 * @property string $phone 手机号
 * @property int $status 状态
 * @property string $last_login_at 最后登录时间
 * @property string $last_login_ip 最后登录IP
 * @property int $login_count 登录次数
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class User extends Model
{
    use SoftDelete;

    // 表名
    protected $name = 'users';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 软删除字段
    protected $deleteTime = 'deleted_at';

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'status' => 'integer',
        'login_count' => 'integer',
        'last_login_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    // 隐藏字段
    protected $hidden = [
        'password',
        'deleted_at'
    ];

    // 追加字段
    protected $append = [
        'status_text',
        'avatar_url'
    ];

    // 允许批量赋值的字段
    protected $field = [
        'username',
        'email',
        'password',
        'avatar',
        'real_name',
        'phone',
        'status'
    ];

    // 状态常量
    const STATUS_DISABLED = 0; // 禁用
    const STATUS_ENABLED = 1;  // 启用

    // 状态文本映射
    const STATUS_TEXT = [
        self::STATUS_DISABLED => '禁用',
        self::STATUS_ENABLED => '启用'
    ];

    /**
     * 密码修改器
     * @param string $value
     * @return string
     */
    public function setPasswordAttr(string $value): string
    {
        return encrypt_password($value);
    }

    /**
     * 状态文本获取器
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getStatusTextAttr($value, array $data): string
    {
        return self::STATUS_TEXT[$data['status']] ?? '未知';
    }

    /**
     * 头像URL获取器
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getAvatarUrlAttr($value, array $data): string
    {
        if (empty($data['avatar'])) {
            return '/static/images/default-avatar.png';
        }

        // 如果是完整URL，直接返回
        if (str_starts_with($data['avatar'], 'http')) {
            return $data['avatar'];
        }

        // 拼接域名
        return request()->domain() . '/' . ltrim($data['avatar'], '/');
    }

    /**
     * 手机号掩码获取器
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getPhoneMaskAttr($value, array $data): string
    {
        return mask_string($data['phone'] ?? '', 3, 4);
    }

    /**
     * 邮箱掩码获取器
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getEmailMaskAttr($value, array $data): string
    {
        $email = $data['email'] ?? '';
        if (empty($email)) {
            return '';
        }

        $parts = explode('@', $email);
        if (count($parts) !== 2) {
            return $email;
        }

        $username = mask_string($parts[0], 2, 2);
        return $username . '@' . $parts[1];
    }

    /**
     * 关联角色
     * @return BelongsToMany
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, UserRole::class, 'role_id', 'user_id');
    }

    /**
     * 验证密码
     * @param string $password 明文密码
     * @return bool
     */
    public function verifyPassword(string $password): bool
    {
        return verify_password($password, $this->password);
    }

    /**
     * 更新登录信息
     * @return bool
     */
    public function updateLoginInfo(): bool
    {
        return $this->save([
            'last_login_at' => date('Y-m-d H:i:s'),
            'last_login_ip' => get_client_ip(),
            'login_count' => $this->login_count + 1
        ]);
    }

    /**
     * 检查用户权限
     * @param string $permission 权限标识
     * @return bool
     */
    public function hasPermission(string $permission): bool
    {
        // 禁用用户没有任何权限
        if ($this->status === self::STATUS_DISABLED) {
            return false;
        }

        // 获取用户所有权限
        $permissions = $this->getAllPermissions();
        return in_array($permission, $permissions);
    }

    /**
     * 获取用户所有权限
     * @return array
     */
    public function getAllPermissions(): array
    {
        $permissions = [];

        // 获取角色权限
        foreach ($this->roles as $role) {
            $rolePermissions = $role->permissions ?? [];
            if (is_string($rolePermissions)) {
                $rolePermissions = json_decode($rolePermissions, true) ?? [];
            }
            $permissions = array_merge($permissions, $rolePermissions);
        }

        return array_unique($permissions);
    }

    /**
     * 检查用户角色
     * @param string $role 角色标识
     * @return bool
     */
    public function hasRole(string $role): bool
    {
        return $this->roles()->where('slug', $role)->exists();
    }

    /**
     * 是否为超级管理员
     * @return bool
     */
    public function isSuperAdmin(): bool
    {
        return $this->hasRole('super_admin');
    }

    /**
     * 分配角色
     * @param array|int $roleIds 角色ID或角色ID数组
     * @return bool
     */
    public function assignRoles($roleIds): bool
    {
        if (!is_array($roleIds)) {
            $roleIds = [$roleIds];
        }

        // 删除现有角色
        UserRole::where('user_id', $this->id)->delete();

        // 分配新角色
        $data = [];
        foreach ($roleIds as $roleId) {
            $data[] = [
                'user_id' => $this->id,
                'role_id' => $roleId,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }

        return UserRole::insertAll($data);
    }

    /**
     * 搜索范围
     * @param \think\db\Query $query
     * @param string $keyword
     * @return void
     */
    public function scopeSearch($query, string $keyword): void
    {
        if (!empty($keyword)) {
            $query->where(function ($q) use ($keyword) {
                $q->whereLike('username', "%{$keyword}%")
                  ->whereOr('email', 'like', "%{$keyword}%")
                  ->whereOr('real_name', 'like', "%{$keyword}%")
                  ->whereOr('phone', 'like', "%{$keyword}%");
            });
        }
    }

    /**
     * 状态范围
     * @param \think\db\Query $query
     * @param int $status
     * @return void
     */
    public function scopeStatus($query, int $status): void
    {
        $query->where('status', $status);
    }

    /**
     * 角色范围
     * @param \think\db\Query $query
     * @param int $roleId
     * @return void
     */
    public function scopeRole($query, int $roleId): void
    {
        $query->whereExists(function ($q) use ($roleId) {
            $q->table('user_roles')
              ->where('user_id', 'exp', 'raw:users.id')
              ->where('role_id', $roleId);
        });
    }

    /**
     * 创建用户
     * @param array $data 用户数据
     * @return static
     */
    public static function createUser(array $data): self
    {
        // 检查用户名是否存在
        if (self::where('username', $data['username'])->exists()) {
            throw new \Exception('用户名已存在');
        }

        // 检查邮箱是否存在
        if (self::where('email', $data['email'])->exists()) {
            throw new \Exception('邮箱已存在');
        }

        // 检查手机号是否存在
        if (!empty($data['phone']) && self::where('phone', $data['phone'])->exists()) {
            throw new \Exception('手机号已存在');
        }

        // 创建用户
        $user = self::create($data);

        // 分配默认角色
        if (!empty($data['role_ids'])) {
            $user->assignRoles($data['role_ids']);
        }

        return $user;
    }

    /**
     * 更新用户
     * @param array $data 用户数据
     * @return bool
     */
    public function updateUser(array $data): bool
    {
        // 检查用户名是否存在（排除自己）
        if (isset($data['username']) && 
            self::where('username', $data['username'])
                ->where('id', '<>', $this->id)
                ->exists()) {
            throw new \Exception('用户名已存在');
        }

        // 检查邮箱是否存在（排除自己）
        if (isset($data['email']) && 
            self::where('email', $data['email'])
                ->where('id', '<>', $this->id)
                ->exists()) {
            throw new \Exception('邮箱已存在');
        }

        // 检查手机号是否存在（排除自己）
        if (isset($data['phone']) && !empty($data['phone']) && 
            self::where('phone', $data['phone'])
                ->where('id', '<>', $this->id)
                ->exists()) {
            throw new \Exception('手机号已存在');
        }

        // 更新用户信息
        $result = $this->save($data);

        // 更新角色
        if (isset($data['role_ids'])) {
            $this->assignRoles($data['role_ids']);
        }

        return $result;
    }

    /**
     * 重置密码
     * @param string $newPassword 新密码
     * @return bool
     */
    public function resetPassword(string $newPassword): bool
    {
        return $this->save(['password' => $newPassword]);
    }

    /**
     * 启用用户
     * @return bool
     */
    public function enable(): bool
    {
        return $this->save(['status' => self::STATUS_ENABLED]);
    }

    /**
     * 禁用用户
     * @return bool
     */
    public function disable(): bool
    {
        return $this->save(['status' => self::STATUS_DISABLED]);
    }
}
