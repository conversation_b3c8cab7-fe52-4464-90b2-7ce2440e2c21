<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 检查表结构
 */

// 数据库配置
$config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'username' => 'root',
    'password' => '123456',
    'database' => 'qiyediy',
    'charset' => 'utf8mb4'
];

echo "=== 检查qd_users表结构 ===\n\n";

try {
    // 连接数据库
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}", 
        $config['username'], 
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // 查看表结构
    $stmt = $pdo->query('DESCRIBE qd_users');
    $fields = $stmt->fetchAll();
    
    echo "字段列表:\n";
    foreach ($fields as $field) {
        echo "- {$field['Field']} ({$field['Type']}) - {$field['Null']} - {$field['Default']}\n";
    }
    
    echo "\n需要的字段:\n";
    $requiredFields = ['id', 'username', 'email', 'password', 'real_name', 'phone', 'avatar', 'status', 'last_login_at', 'last_login_ip', 'login_count', 'created_at', 'updated_at', 'deleted_at'];
    
    $existingFields = array_column($fields, 'Field');
    $missingFields = array_diff($requiredFields, $existingFields);
    
    if (empty($missingFields)) {
        echo "✅ 所有必需字段都存在\n";
    } else {
        echo "❌ 缺少字段: " . implode(', ', $missingFields) . "\n";
        
        // 添加缺少的字段
        echo "\n添加缺少的字段...\n";
        
        $alterStatements = [];
        foreach ($missingFields as $field) {
            switch ($field) {
                case 'updated_at':
                    $alterStatements[] = "ADD COLUMN updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'";
                    break;
                case 'last_login_ip':
                    $alterStatements[] = "ADD COLUMN last_login_ip varchar(45) NULL DEFAULT NULL COMMENT '最后登录IP'";
                    break;
                case 'login_count':
                    $alterStatements[] = "ADD COLUMN login_count int(11) NOT NULL DEFAULT 0 COMMENT '登录次数'";
                    break;
            }
        }
        
        foreach ($alterStatements as $statement) {
            try {
                $pdo->exec("ALTER TABLE qd_users {$statement}");
                echo "✅ 字段添加成功: {$statement}\n";
            } catch (Exception $e) {
                echo "❌ 字段添加失败: {$statement} - {$e->getMessage()}\n";
            }
        }
    }

} catch (Exception $e) {
    echo "❌ 操作失败: " . $e->getMessage() . "\n";
    exit(1);
}
