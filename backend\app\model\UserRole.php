<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 用户角色关联模型
 */

declare(strict_types=1);

namespace app\model;

use think\model\Pivot;

/**
 * 用户角色关联模型
 * @property int $id ID
 * @property int $user_id 用户ID
 * @property int $role_id 角色ID
 * @property string $created_at 创建时间
 */
class UserRole extends Pivot
{
    // 表名
    protected $name = 'user_roles';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'created_at';
    protected $updateTime = false;

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'user_id' => 'integer',
        'role_id' => 'integer',
        'created_at' => 'datetime'
    ];

    // 允许批量赋值的字段
    protected $field = [
        'user_id',
        'role_id'
    ];
}
