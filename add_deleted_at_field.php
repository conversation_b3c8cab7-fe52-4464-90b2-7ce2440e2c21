<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 添加软删除字段
 */

// 数据库配置
$config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'username' => 'root',
    'password' => '123456',
    'database' => 'qiyediy',
    'charset' => 'utf8mb4'
];

echo "=== 添加软删除字段 ===\n\n";

try {
    // 连接数据库
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}", 
        $config['username'], 
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    echo "✅ 数据库连接成功\n\n";

    // 检查字段是否已存在
    $stmt = $pdo->query("SHOW COLUMNS FROM qd_users LIKE 'deleted_at'");
    if ($stmt->rowCount() > 0) {
        echo "⚠️  deleted_at字段已存在\n";
    } else {
        // 添加deleted_at字段
        echo "添加deleted_at字段到qd_users表...\n";
        $pdo->exec("ALTER TABLE qd_users ADD COLUMN deleted_at timestamp NULL DEFAULT NULL COMMENT '删除时间'");
        echo "✅ deleted_at字段添加成功\n";
    }

    // 验证字段
    $stmt = $pdo->query("SHOW COLUMNS FROM qd_users LIKE 'deleted_at'");
    if ($stmt->rowCount() > 0) {
        echo "✅ deleted_at字段验证成功\n";
    } else {
        echo "❌ deleted_at字段验证失败\n";
    }

    echo "\n=== 字段添加完成 ===\n";

} catch (Exception $e) {
    echo "❌ 操作失败: " . $e->getMessage() . "\n";
    exit(1);
}
