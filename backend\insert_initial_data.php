<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 插入初始数据脚本
 */

// 数据库配置
$config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'username' => 'root',
    'password' => '123456',
    'database' => 'qiyediy',
    'charset' => 'utf8mb4'
];

echo "=== 插入初始数据 ===\n\n";

try {
    // 连接数据库
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}", 
        $config['username'], 
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    echo "✅ 数据库连接成功\n\n";

    // 1. 插入默认角色
    echo "1. 插入默认角色...\n";
    $rolesSql = "INSERT INTO `qd_roles` (`id`, `name`, `slug`, `description`, `permissions`, `is_default`, `sort_order`) VALUES
    (1, '超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', '[\"*\"]', 0, 1),
    (2, '管理员', 'admin', '系统管理员，拥有大部分管理权限', '[\"user.*\", \"role.*\", \"diy.*\", \"content.*\", \"system.view\"]', 0, 2),
    (3, '编辑员', 'editor', '内容编辑员，负责内容管理', '[\"content.*\", \"diy.view\", \"diy.create\", \"diy.update\"]', 0, 3),
    (4, '普通用户', 'user', '普通注册用户', '[\"diy.view\", \"diy.create\", \"content.view\"]', 1, 4)
    ON DUPLICATE KEY UPDATE `name` = VALUES(`name`)";
    
    $pdo->exec($rolesSql);
    echo "✅ 角色数据插入成功\n";

    // 2. 插入默认管理员账号
    echo "2. 插入默认管理员账号...\n";
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $userSql = "INSERT INTO `qd_users` (`id`, `username`, `email`, `password`, `real_name`, `status`, `created_at`) VALUES
    (1, 'admin', '<EMAIL>', ?, '系统管理员', 1, NOW())
    ON DUPLICATE KEY UPDATE `email` = VALUES(`email`)";
    
    $stmt = $pdo->prepare($userSql);
    $stmt->execute([$adminPassword]);
    echo "✅ 管理员账号插入成功\n";

    // 3. 分配超级管理员角色
    echo "3. 分配超级管理员角色...\n";
    $userRoleSql = "INSERT INTO `qd_user_roles` (`user_id`, `role_id`) VALUES (1, 1)
    ON DUPLICATE KEY UPDATE `role_id` = VALUES(`role_id`)";
    
    $pdo->exec($userRoleSql);
    echo "✅ 角色分配成功\n";

    // 4. 插入基础权限
    echo "4. 插入基础权限...\n";
    $permissions = [
        ['查看用户', 'user.view', '查看用户列表和详情', 'user', 'view'],
        ['创建用户', 'user.create', '创建新用户', 'user', 'create'],
        ['更新用户', 'user.update', '更新用户信息', 'user', 'update'],
        ['删除用户', 'user.delete', '删除用户', 'user', 'delete'],
        ['重置密码', 'user.reset_password', '重置用户密码', 'user', 'reset_password'],
        ['分配角色', 'user.assign_roles', '为用户分配角色', 'user', 'assign_roles'],
        ['查看角色', 'role.view', '查看角色列表和详情', 'role', 'view'],
        ['创建角色', 'role.create', '创建新角色', 'role', 'create'],
        ['更新角色', 'role.update', '更新角色信息', 'role', 'update'],
        ['删除角色', 'role.delete', '删除角色', 'role', 'delete'],
        ['查看DIY页面', 'diy.view', '查看DIY页面', 'diy', 'view'],
        ['创建DIY页面', 'diy.create', '创建DIY页面', 'diy', 'create'],
        ['更新DIY页面', 'diy.update', '更新DIY页面', 'diy', 'update'],
        ['删除DIY页面', 'diy.delete', '删除DIY页面', 'diy', 'delete'],
        ['发布DIY页面', 'diy.publish', '发布DIY页面', 'diy', 'publish'],
        ['查看内容', 'content.view', '查看内容列表', 'content', 'view'],
        ['创建内容', 'content.create', '创建新内容', 'content', 'create'],
        ['更新内容', 'content.update', '更新内容', 'content', 'update'],
        ['删除内容', 'content.delete', '删除内容', 'content', 'delete'],
        ['发布内容', 'content.publish', '发布内容', 'content', 'publish'],
        ['查看系统', 'system.view', '查看系统信息', 'system', 'view'],
        ['系统设置', 'system.settings', '修改系统设置', 'system', 'settings'],
        ['查看日志', 'system.logs', '查看系统日志', 'system', 'logs']
    ];

    $permissionSql = "INSERT INTO `qd_permissions` (`name`, `slug`, `description`, `module`, `action`) VALUES (?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE `name` = VALUES(`name`)";
    $stmt = $pdo->prepare($permissionSql);
    
    foreach ($permissions as $permission) {
        $stmt->execute($permission);
    }
    echo "✅ 权限数据插入成功\n";

    // 5. 插入默认DIY组件
    echo "5. 插入默认DIY组件...\n";
    $components = [
        ['文本组件', 'text', 'basic', 'basic', '基础文本显示组件', 'text', '{"editable": true, "styles": ["fontSize", "color", "textAlign"]}', '{"text": "请输入文本内容", "fontSize": "16px", "color": "#333333"}', 1, 1, 1],
        ['图片组件', 'image', 'basic', 'basic', '图片显示组件', 'image', '{"editable": true, "styles": ["width", "height", "borderRadius"]}', '{"src": "", "alt": "图片", "width": "100%"}', 1, 1, 2],
        ['按钮组件', 'button', 'basic', 'basic', '按钮交互组件', 'button', '{"editable": true, "styles": ["backgroundColor", "color", "borderRadius"]}', '{"text": "按钮", "link": "", "backgroundColor": "#007bff", "color": "#ffffff"}', 1, 1, 3],
        ['容器组件', 'container', 'layout', 'layout', '布局容器组件', 'container', '{"editable": true, "styles": ["padding", "margin", "backgroundColor"]}', '{"padding": "20px", "backgroundColor": "#ffffff"}', 1, 1, 4]
    ];

    $componentSql = "INSERT INTO `qd_diy_components` (`name`, `slug`, `type`, `category`, `description`, `icon`, `config`, `default_props`, `is_system`, `is_active`, `sort_order`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE `name` = VALUES(`name`)";
    $stmt = $pdo->prepare($componentSql);
    
    foreach ($components as $component) {
        $stmt->execute($component);
    }
    echo "✅ DIY组件数据插入成功\n";

    // 6. 插入默认模板
    echo "6. 插入默认模板...\n";
    $templates = [
        ['企业官网模板', 'corporate-website', '适合企业官网的专业模板', '{"components": [], "layout": "default"}', 'corporate', 1, 1, 1],
        ['产品展示模板', 'product-showcase', '适合产品展示的现代模板', '{"components": [], "layout": "product"}', 'product', 1, 1, 2],
        ['个人博客模板', 'personal-blog', '适合个人博客的简洁模板', '{"components": [], "layout": "blog"}', 'blog', 1, 1, 3]
    ];

    $templateSql = "INSERT INTO `qd_diy_templates` (`name`, `slug`, `description`, `content`, `category`, `is_system`, `is_free`, `sort_order`) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE `name` = VALUES(`name`)";
    $stmt = $pdo->prepare($templateSql);
    
    foreach ($templates as $template) {
        $stmt->execute($template);
    }
    echo "✅ 模板数据插入成功\n\n";

    // 7. 验证数据
    echo "=== 验证数据 ===\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM qd_users");
    $userCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM qd_roles");
    $roleCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM qd_permissions");
    $permissionCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM qd_diy_components");
    $componentCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM qd_diy_templates");
    $templateCount = $stmt->fetch()['count'];

    echo "✅ 用户数: {$userCount}\n";
    echo "✅ 角色数: {$roleCount}\n";
    echo "✅ 权限数: {$permissionCount}\n";
    echo "✅ 组件数: {$componentCount}\n";
    echo "✅ 模板数: {$templateCount}\n\n";

    echo "=== 初始数据插入完成 ===\n";
    echo "默认管理员账号:\n";
    echo "  用户名: admin\n";
    echo "  密码: admin123\n";
    echo "  邮箱: <EMAIL>\n";

} catch (Exception $e) {
    echo "❌ 插入失败: " . $e->getMessage() . "\n";
    exit(1);
}
