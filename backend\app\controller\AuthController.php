<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 认证控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\model\User;
use app\service\AuthService;
use app\validate\AuthValidate;
use think\Response;

/**
 * 认证控制器
 */
class AuthController extends BaseController
{
    protected AuthService $authService;

    protected function initialize(): void
    {
        parent::initialize();
        $this->authService = new AuthService();
    }

    /**
     * 用户登录
     * @return Response
     */
    public function login(): Response
    {
        try {
            $data = $this->post();

            // 数据验证
            $this->validate($data, AuthValidate::class . '.login');

            // 执行登录
            $result = $this->authService->login($data['username'], $data['password']);

            // 记录操作日志
            $this->logOperation('login', 'user', $result['user']['id']);

            return $this->success($result, '登录成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 用户注册
     * @return Response
     */
    public function register(): Response
    {
        try {
            $data = $this->post();

            // 数据验证
            $this->validate($data, AuthValidate::class . '.register');

            // 执行注册
            $result = $this->authService->register($data);

            // 记录操作日志
            $this->logOperation('register', 'user', $result['user']['id']);

            return $this->success($result, '注册成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 用户登出
     * @return Response
     */
    public function logout(): Response
    {
        try {
            $token = $this->request->header('Authorization');
            if ($token) {
                $token = str_replace('Bearer ', '', $token);
                $this->authService->logout($token);
            }

            // 记录操作日志
            $this->logOperation('logout', 'user', $this->getUserId());

            return $this->success([], '登出成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 刷新Token
     * @return Response
     */
    public function refresh(): Response
    {
        try {
            $token = $this->request->header('Authorization');
            if (!$token) {
                return $this->error('Token不能为空', 401);
            }

            $token = str_replace('Bearer ', '', $token);
            $result = $this->authService->refreshToken($token);

            return $this->success($result, 'Token刷新成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage(), 401);
        }
    }

    /**
     * 获取当前用户信息
     * @return Response
     */
    public function me(): Response
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->error('用户未登录', 401);
            }

            // 获取完整用户信息
            $userModel = User::with(['roles'])->find($user['id']);
            if (!$userModel) {
                return $this->error('用户不存在', 404);
            }

            // 获取用户权限
            $permissions = $userModel->getAllPermissions();

            $result = [
                'user' => $userModel->toArray(),
                'permissions' => $permissions,
                'roles' => $userModel->roles->toArray()
            ];

            return $this->success($result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 修改密码
     * @return Response
     */
    public function changePassword(): Response
    {
        try {
            $data = $this->post();

            // 数据验证
            $this->validate($data, AuthValidate::class . '.changePassword');

            $userId = $this->getUserId();
            if (!$userId) {
                return $this->error('用户未登录', 401);
            }

            // 执行修改密码
            $this->authService->changePassword($userId, $data['old_password'], $data['new_password']);

            // 记录操作日志
            $this->logOperation('change_password', 'user', $userId);

            return $this->success([], '密码修改成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 忘记密码 - 发送重置邮件
     * @return Response
     */
    public function forgotPassword(): Response
    {
        try {
            $data = $this->post();

            // 数据验证
            $this->validate($data, AuthValidate::class . '.forgotPassword');

            // 发送重置邮件
            $this->authService->sendResetPasswordEmail($data['email']);

            return $this->success([], '重置密码邮件已发送');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 重置密码
     * @return Response
     */
    public function resetPassword(): Response
    {
        try {
            $data = $this->post();

            // 数据验证
            $this->validate($data, AuthValidate::class . '.resetPassword');

            // 执行重置密码
            $this->authService->resetPassword($data['token'], $data['password']);

            return $this->success([], '密码重置成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 发送验证码
     * @return Response
     */
    public function sendCode(): Response
    {
        try {
            $data = $this->post();

            // 数据验证
            $this->validate($data, AuthValidate::class . '.sendCode');

            // 发送验证码
            $this->authService->sendVerificationCode($data['phone'], $data['type']);

            return $this->success([], '验证码已发送');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 验证验证码
     * @return Response
     */
    public function verifyCode(): Response
    {
        try {
            $data = $this->post();

            // 数据验证
            $this->validate($data, AuthValidate::class . '.verifyCode');

            // 验证验证码
            $result = $this->authService->verifyCode($data['phone'], $data['code'], $data['type']);

            return $this->success($result, '验证成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 绑定手机号
     * @return Response
     */
    public function bindPhone(): Response
    {
        try {
            $data = $this->post();

            // 数据验证
            $this->validate($data, AuthValidate::class . '.bindPhone');

            $userId = $this->getUserId();
            if (!$userId) {
                return $this->error('用户未登录', 401);
            }

            // 绑定手机号
            $this->authService->bindPhone($userId, $data['phone'], $data['code']);

            // 记录操作日志
            $this->logOperation('bind_phone', 'user', $userId, ['phone' => $data['phone']]);

            return $this->success([], '手机号绑定成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新个人资料
     * @return Response
     */
    public function updateProfile(): Response
    {
        try {
            $data = $this->post();

            // 数据验证
            $this->validate($data, AuthValidate::class . '.updateProfile');

            $userId = $this->getUserId();
            if (!$userId) {
                return $this->error('用户未登录', 401);
            }

            // 更新个人资料
            $result = $this->authService->updateProfile($userId, $data);

            // 记录操作日志
            $this->logOperation('update_profile', 'user', $userId, $data);

            return $this->success($result, '个人资料更新成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 上传头像
     * @return Response
     */
    public function uploadAvatar(): Response
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                return $this->error('用户未登录', 401);
            }

            // 处理文件上传
            $fileInfo = $this->handleUpload('avatar', [
                'size' => 5 * 1024 * 1024, // 5MB
                'ext' => 'jpg,jpeg,png,gif',
                'type' => 'avatar'
            ]);

            // 更新用户头像
            $user = User::find($userId);
            $user->save(['avatar' => $fileInfo['path']]);

            // 记录操作日志
            $this->logOperation('upload_avatar', 'user', $userId, $fileInfo);

            return $this->success([
                'avatar' => $fileInfo['url'],
                'avatar_url' => $user->avatar_url
            ], '头像上传成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 检查用户名是否可用
     * @return Response
     */
    public function checkUsername(): Response
    {
        try {
            $username = $this->get('username');
            if (empty($username)) {
                return $this->error('用户名不能为空');
            }

            $exists = User::where('username', $username)->exists();

            return $this->success([
                'available' => !$exists,
                'message' => $exists ? '用户名已存在' : '用户名可用'
            ]);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 检查邮箱是否可用
     * @return Response
     */
    public function checkEmail(): Response
    {
        try {
            $email = $this->get('email');
            if (empty($email)) {
                return $this->error('邮箱不能为空');
            }

            $exists = User::where('email', $email)->exists();

            return $this->success([
                'available' => !$exists,
                'message' => $exists ? '邮箱已存在' : '邮箱可用'
            ]);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 检查手机号是否可用
     * @return Response
     */
    public function checkPhone(): Response
    {
        try {
            $phone = $this->get('phone');
            if (empty($phone)) {
                return $this->error('手机号不能为空');
            }

            $exists = User::where('phone', $phone)->exists();

            return $this->success([
                'available' => !$exists,
                'message' => $exists ? '手机号已存在' : '手机号可用'
            ]);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
