<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 角色管理控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\model\Role;
use think\Response;

/**
 * 角色管理控制器
 */
class RoleController extends BaseController
{
    protected function initialize(): void
    {
        parent::initialize();
    }

    /**
     * 角色列表
     * @return Response
     */
    public function index(): Response
    {
        try {
            $page = $this->request->param('page', 1);
            $limit = $this->request->param('limit', 15);
            $keyword = $this->request->param('keyword', '');

            $query = Role::field('*');

            if (!empty($keyword)) {
                $query->where(function($q) use ($keyword) {
                    $q->whereLike('name', "%{$keyword}%")
                      ->whereOr('slug', 'like', "%{$keyword}%")
                      ->whereOr('description', 'like', "%{$keyword}%");
                });
            }

            $result = $query->order('sort_order', 'asc')
                          ->order('id', 'desc')
                          ->paginate([
                              'list_rows' => $limit,
                              'page' => $page
                          ]);

            return $this->success($result->toArray(), '获取角色列表成功');

        } catch (\Exception $e) {
            return $this->error('获取角色列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 角色详情
     * @param int $id 角色ID
     * @return Response
     */
    public function read(int $id): Response
    {
        try {
            $role = Role::find($id);
            if (!$role) {
                return $this->error('角色不存在', 404);
            }

            return $this->success($role->toArray(), '获取角色详情成功');

        } catch (\Exception $e) {
            return $this->error('获取角色详情失败: ' . $e->getMessage());
        }
    }






}
