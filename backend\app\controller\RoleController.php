<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 角色管理控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\model\Role;
use think\Response;

/**
 * 角色管理控制器
 */
class RoleController extends BaseController
{
    protected function initialize(): void
    {
        parent::initialize();
    }

    /**
     * 角色列表
     * @return Response
     */
    public function index(): Response
    {
        try {
            $page = $this->request->param('page', 1);
            $limit = $this->request->param('limit', 15);
            $keyword = $this->request->param('keyword', '');

            $query = Role::withCount(['users']);

            if (!empty($keyword)) {
                $query->where(function($q) use ($keyword) {
                    $q->whereLike('name', "%{$keyword}%")
                      ->whereOr('slug', 'like', "%{$keyword}%")
                      ->whereOr('description', 'like', "%{$keyword}%");
                });
            }

            $result = $query->order('sort_order', 'asc')
                          ->order('id', 'desc')
                          ->paginate([
                              'list_rows' => $limit,
                              'page' => $page
                          ]);

            return $this->success($result->toArray(), '获取角色列表成功');

        } catch (\Exception $e) {
            return $this->error('获取角色列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 角色详情
     * @param int $id 角色ID
     * @return Response
     */
    public function read(int $id): Response
    {
        try {
            $role = Role::withCount(['users'])->find($id);
            if (!$role) {
                return $this->error('角色不存在', 404);
            }

            return $this->success($role->toArray(), '获取角色详情成功');

        } catch (\Exception $e) {
            return $this->error('获取角色详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取权限列表
     * @return Response
     */
    public function permissions(): Response
    {
        try {
            // 返回基础权限列表
            $permissions = [
                'user' => [
                    'name' => '用户管理',
                    'permissions' => [
                        'user.view' => '查看用户',
                        'user.create' => '创建用户',
                        'user.update' => '更新用户',
                        'user.delete' => '删除用户',
                    ]
                ],
                'role' => [
                    'name' => '角色管理',
                    'permissions' => [
                        'role.view' => '查看角色',
                        'role.create' => '创建角色',
                        'role.update' => '更新角色',
                        'role.delete' => '删除角色',
                    ]
                ],
                'diy' => [
                    'name' => 'DIY管理',
                    'permissions' => [
                        'diy.view' => '查看DIY',
                        'diy.create' => '创建DIY',
                        'diy.update' => '更新DIY',
                        'diy.delete' => '删除DIY',
                    ]
                ],
                'system' => [
                    'name' => '系统管理',
                    'permissions' => [
                        'system.view' => '查看系统',
                        'system.update' => '更新系统',
                        'system.backup' => '系统备份',
                    ]
                ]
            ];

            return $this->success($permissions, '获取权限列表成功');

        } catch (\Exception $e) {
            return $this->error('获取权限列表失败: ' . $e->getMessage());
        }
    }




}
