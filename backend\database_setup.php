<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 数据库安装脚本
 */

// 数据库配置
$config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'username' => 'root',
    'password' => '123456',
    'database' => 'qiyediy',
    'charset' => 'utf8mb4'
];

echo "=== QiyeDIY数据库安装脚本 ===\n\n";

try {
    // 1. 连接MySQL服务器（不指定数据库）
    echo "1. 连接MySQL服务器...\n";
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};charset={$config['charset']}", 
        $config['username'], 
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    echo "✅ MySQL连接成功\n\n";

    // 2. 检查数据库是否存在
    echo "2. 检查数据库是否存在...\n";
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$config['database']}'");
    $dbExists = $stmt->rowCount() > 0;
    
    if ($dbExists) {
        echo "⚠️  数据库 '{$config['database']}' 已存在\n";
        echo "是否要重新创建数据库？这将删除所有现有数据！(y/N): ";
        $handle = fopen("php://stdin", "r");
        $input = trim(fgets($handle));
        fclose($handle);
        
        if (strtolower($input) === 'y' || strtolower($input) === 'yes') {
            echo "删除现有数据库...\n";
            $pdo->exec("DROP DATABASE IF EXISTS `{$config['database']}`");
            echo "✅ 数据库已删除\n";
        } else {
            echo "❌ 安装已取消\n";
            exit(1);
        }
    }

    // 3. 创建数据库
    echo "3. 创建数据库...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ 数据库 '{$config['database']}' 创建成功\n\n";

    // 4. 选择数据库
    $pdo->exec("USE `{$config['database']}`");

    // 5. 读取并执行SQL文件
    echo "4. 导入数据库结构和初始数据...\n";
    $sqlFile = __DIR__ . '/database/qiyediy.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: {$sqlFile}");
    }

    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("无法读取SQL文件");
    }

    // 分割SQL语句并执行
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^(--|\/\*|\s*$)/', $stmt);
        }
    );

    $successCount = 0;
    $errorCount = 0;

    foreach ($statements as $statement) {
        try {
            $pdo->exec($statement);
            $successCount++;
        } catch (PDOException $e) {
            $errorCount++;
            echo "⚠️  SQL执行警告: " . $e->getMessage() . "\n";
            echo "语句: " . substr($statement, 0, 100) . "...\n";
        }
    }

    echo "✅ SQL导入完成: {$successCount} 条成功, {$errorCount} 条警告\n\n";

    // 6. 验证安装
    echo "5. 验证安装结果...\n";
    
    // 检查表是否创建成功
    $tables = [
        'qd_users', 'qd_roles', 'qd_user_roles', 'qd_permissions',
        'qd_diy_components', 'qd_diy_templates', 'qd_diy_pages',
        'qd_contents', 'qd_content_categories', 'qd_media_files'
    ];

    $createdTables = [];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->rowCount() > 0) {
            $createdTables[] = $table;
        }
    }

    echo "✅ 已创建 " . count($createdTables) . "/" . count($tables) . " 个数据表\n";

    // 检查默认数据
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM qd_users");
    $userCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM qd_roles");
    $roleCount = $stmt->fetch()['count'];

    echo "✅ 默认用户数: {$userCount}\n";
    echo "✅ 默认角色数: {$roleCount}\n\n";

    // 7. 显示默认账号信息
    echo "=== 安装完成 ===\n";
    echo "数据库: {$config['database']}\n";
    echo "默认管理员账号:\n";
    echo "  用户名: admin\n";
    echo "  密码: admin123\n";
    echo "  邮箱: <EMAIL>\n\n";
    echo "现在可以使用登录API进行测试了！\n";

} catch (Exception $e) {
    echo "❌ 安装失败: " . $e->getMessage() . "\n";
    exit(1);
}
