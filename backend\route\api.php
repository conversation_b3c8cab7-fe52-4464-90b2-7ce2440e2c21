<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - API路由配置
 */

use think\facade\Route;

// 认证相关路由（无需认证）
Route::post('api/auth/login', 'AuthController@login')->middleware('cors');
Route::post('api/auth/register', 'AuthController@register')->middleware('cors');
Route::post('api/auth/forgot-password', 'AuthController@forgotPassword')->middleware('cors');
Route::post('api/auth/reset-password', 'AuthController@resetPassword')->middleware('cors');
Route::post('api/auth/verify-email', 'AuthController@verifyEmail')->middleware('cors');
Route::post('api/auth/refresh-token', 'AuthController@refresh')->middleware('cors');

// 认证信息（需要认证）
Route::get('api/auth/me', 'Auth<PERSON><PERSON>roller@me')->middleware(['cors', 'auth']);
Route::post('api/auth/logout', 'AuthController@logout')->middleware(['cors', 'auth']);
Route::post('api/auth/change-password', 'AuthController@changePassword')->middleware(['cors', 'auth']);
Route::put('api/auth/profile', 'AuthController@updateProfile')->middleware(['cors', 'auth']);

// API版本前缀（需要认证）
Route::group('api', function () {

        // 仪表盘
        Route::group('dashboard', function () {
            Route::get('stats', 'DashboardController@stats');
            Route::get('activities', 'DashboardController@activities');
            Route::get('visit-trend', 'DashboardController@visitTrend');
            Route::get('quick-stats', 'DashboardController@quickStats');
        });
        
        // 用户管理
        Route::resource('user', 'UserController');
        Route::group('user', function () {
            Route::post(':id/enable', 'UserController@enable');
            Route::post(':id/disable', 'UserController@disable');
            Route::post(':id/reset-password', 'UserController@resetPassword');
            Route::post(':id/assign-roles', 'UserController@assignRoles');
            Route::delete('batch', 'UserController@batchDelete');
            Route::get('statistics', 'UserController@statistics');
            Route::get('export', 'UserController@export');
            Route::post('import', 'UserController@import');
            Route::get('check-username', 'UserController@checkUsername');
            Route::get('check-email', 'UserController@checkEmail');
            Route::get('check-phone', 'UserController@checkPhone');
            Route::get(':id/logs', 'UserController@getLogs');
            Route::get(':id/login-history', 'UserController@getLoginHistory');
        });
        
        // 角色管理
        Route::resource('role', 'RoleController');
        Route::group('role', function () {
            Route::get('permissions', 'RoleController@getPermissions');
            Route::post(':id/assign-permissions', 'RoleController@assignPermissions');
            Route::delete('batch', 'RoleController@batchDelete');
        });
        
        // DIY页面管理
        Route::resource('diy-page', 'DiyPageController');
        Route::group('diy-page', function () {
            Route::post(':id/publish', 'DiyPageController@publish');
            Route::post(':id/unpublish', 'DiyPageController@unpublish');
            Route::post(':id/copy', 'DiyPageController@copy');
            Route::delete('batch', 'DiyPageController@batchDelete');
            Route::get('statistics', 'DiyPageController@statistics');
        });

        // DIY模板管理
        Route::resource('diy-template', 'DiyTemplateController');
        Route::group('diy-template', function () {
            Route::get('categories', 'DiyTemplateController@getCategories');
            Route::post(':id/copy', 'DiyTemplateController@copy');
            Route::delete('batch', 'DiyTemplateController@batchDelete');
        });

        // DIY统一管理（新增）
        Route::group('diy', function () {
            // 页面管理
            Route::get('page', 'DiyController@getPages');
            Route::get('page/:id', 'DiyController@getPage');
            Route::post('page', 'DiyController@createPage');
            Route::put('page/:id', 'DiyController@updatePage');
            Route::delete('page/:id', 'DiyController@deletePage');
            Route::post('page/:id/publish', 'DiyController@publishPage');
            Route::post('page/:id/unpublish', 'DiyController@unpublishPage');
            Route::post('page/:id/content', 'DiyController@savePageContent');
            Route::get('page/:id/preview', 'DiyController@previewPage');

            // 组件管理
            Route::get('component', 'DiyController@getComponents');
            Route::get('component/:id', 'DiyController@getComponent');
            Route::post('component', 'DiyController@createComponent');
            Route::put('component/:id', 'DiyController@updateComponent');
            Route::delete('component/:id', 'DiyController@deleteComponent');

            // 模板管理
            Route::get('template', 'DiyController@getTemplates');
            Route::get('template/all', 'DiyController@getAllTemplates');
            Route::get('template/:id', 'DiyController@getTemplate');
            Route::post('template', 'DiyController@createTemplate');
            Route::put('template/:id', 'DiyController@updateTemplate');
            Route::delete('template/:id', 'DiyController@deleteTemplate');
            Route::post('template/:id/copy', 'DiyController@copyTemplate');

            // 统计和批量操作
            Route::get('statistics', 'DiyController@getStatistics');
            Route::delete('batch', 'DiyController@batchDelete');
        });
        
        // 文件上传
        Route::group('upload', function () {
            Route::post('image', 'UploadController@image');
            Route::post('video', 'UploadController@video');
            Route::post('document', 'UploadController@document');
            Route::post('avatar', 'UploadController@avatar');
            Route::post('batch', 'UploadController@batch');
            Route::post('base64', 'UploadController@base64');
            Route::get('config', 'UploadController@config');
            Route::delete('delete', 'UploadController@delete');
            Route::get('info', 'UploadController@info');
            Route::post('thumbnail', 'UploadController@thumbnail');
            Route::post('crop', 'UploadController@crop');
            Route::post('compress', 'UploadController@compress');
            Route::get('statistics', 'UploadController@statistics');
        });
        
        // 搜索功能
        Route::group('search', function () {
            Route::get('global', 'SearchController@global');
            Route::get('users', 'SearchController@users');
            Route::get('pages', 'SearchController@pages');
            Route::get('templates', 'SearchController@templates');
        });
        
        // 系统管理
        Route::group('system', function () {
            Route::get('info', 'SystemController@info');
            Route::get('settings', 'SystemController@getSettings');
            Route::post('settings', 'SystemController@updateSettings');
            Route::get('cache/clear', 'SystemController@clearCache');
            Route::get('logs', 'SystemController@getLogs');
            Route::delete('logs', 'SystemController@clearLogs');
        });
        
        // 操作日志
        Route::group('log', function () {
            Route::get('list', 'LogController@index');
            Route::get(':id', 'LogController@read');
            Route::delete('batch', 'LogController@batchDelete');
            Route::get('statistics', 'LogController@statistics');
        });
        
        // API文档
        Route::get('docs', 'ApiDocController@index');

})->middleware(['cors', 'auth']);

// 公开API路由（前端展示用）
Route::group('api/public', function () {
    
    // 模板展示
    Route::get('templates/featured', 'DiyTemplateController@getFeatured');
    Route::get('templates/categories', 'DiyTemplateController@getPublicCategories');
    Route::get('template/:slug', 'DiyTemplateController@getBySlug');
    
    // 页面展示
    Route::get('page/:slug', 'DiyPageController@getBySlug');
    Route::get('pages/published', 'DiyPageController@getPublished');
    
    // 内容展示
    Route::get('contents/:type', 'ContentController@getByType');
    Route::get('content/:slug', 'ContentController@getBySlug');
    
    // 站点信息
    Route::get('site/info', 'SystemController@getSiteInfo');

})->middleware('cors');

// 测试API路由（无需认证）
Route::group('api/test', function () {
    Route::get('index', 'TestController@index');
    Route::get('database', 'TestController@database');
    Route::get('diy-stats', 'TestController@diyStats');
})->middleware('cors');

// 公开路由（无需认证）
Route::get('docs', 'Index@docs')->middleware('cors');
